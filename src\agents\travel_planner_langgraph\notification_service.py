"""
NotificationService - V2实时推送与状态追踪服务

基于推送.md文档要求实现的V2核心通知服务，负责：
- Redis Pub/Sub事件驱动推送
- 生成唯一的step_id
- 推送step_start和step_end事件
- 支持页面刷新后状态恢复
- 完全解耦的事件驱动架构
"""

import json
import uuid
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
import asyncio

logger = logging.getLogger(__name__)


class NotificationService:
    """
    V2实时通知服务

    基于Redis Pub/Sub的事件驱动推送服务，为LangGraph的每个关键节点
    提供实时推送功能，完全解耦业务逻辑与推送机制。
    """

    def __init__(self, redis_client=None, session_id: str = None):
        """
        初始化V2通知服务

        Args:
            redis_client: Redis客户端实例（必需，用于Pub/Sub）
            session_id: 会话ID
        """
        self.redis_client = redis_client
        self.session_id = session_id or f"session_{int(datetime.now().timestamp())}"
        self.step_counter = 0
        self.active_steps: Dict[str, Dict[str, Any]] = {}

        # V2架构：Redis Pub/Sub频道
        self.channel = f"task:{self.session_id}"

    def generate_step_id(self) -> str:
        """生成唯一的步骤ID"""
        self.step_counter += 1
        return f"{self.session_id}_step_{self.step_counter}_{uuid.uuid4().hex[:8]}"

    async def _publish(self, event_data: Dict[str, Any]):
        """
        V2架构核心方法：发布事件到Redis Pub/Sub

        Args:
            event_data: 事件数据
        """
        if not self.redis_client:
            logger.warning(f"Redis客户端未配置，无法发布事件: {event_data.get('event', 'unknown')}")
            return

        try:
            message = json.dumps(event_data, ensure_ascii=False)
            await self.redis_client.publish(self.channel, message)
            logger.debug(f"Published to {self.channel}: {event_data.get('event', 'unknown')}")
        except Exception as e:
            logger.error(f"Redis Pub/Sub发布失败: {str(e)}")
            raise
    
    async def notify_step_start(
        self,
        step_name: str,
        title: str,
        message: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        V2架构：推送步骤开始事件到Redis Pub/Sub

        Args:
            step_name: 步骤名称，如'core_intent_analysis'
            title: 显示标题，如'解析用户需求和画像'
            message: 状态消息，如'正在分析您的旅行意图...'
            metadata: 额外的元数据

        Returns:
            step_id: 步骤ID
        """
        step_id = self.generate_step_id()
        timestamp = datetime.now().isoformat()

        # 构建事件数据（符合推送.md规范）
        event_data = {
            "step_id": step_id,
            "step_name": step_name,
            "title": title,
            "message": message,
            "timestamp": timestamp
        }

        if metadata:
            event_data.update(metadata)

        # V2架构：构建标准事件格式
        event = {
            "event": "step_start",
            "data": event_data
        }

        # 记录活跃步骤
        self.active_steps[step_id] = {
            "step_name": step_name,
            "title": title,
            "start_time": timestamp,
            "status": "running"
        }

        # V2架构：发布到Redis Pub/Sub
        await self._publish(event)

        logger.info(f"步骤开始: {step_name} ({step_id}) - {title}")
        return step_id
    
    async def notify_step_end(
        self,
        step_id: str,
        status: str = "success",
        result: Optional[Dict[str, Any]] = None,
        message: str = "",
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        V2架构：推送步骤结束事件到Redis Pub/Sub

        Args:
            step_id: 步骤ID（由notify_step_start返回）
            status: 状态，'success'或'error'
            result: 步骤结果数据
            message: 结束消息
            metadata: 额外的元数据
        """
        timestamp = datetime.now().isoformat()

        # 获取步骤信息
        step_info = self.active_steps.get(step_id, {})
        step_name = step_info.get("step_name", "unknown")

        # 构建事件数据（符合推送.md规范）
        event_data = {
            "step_id": step_id,
            "step_name": step_name,
            "status": status,
            "timestamp": timestamp
        }

        if result:
            event_data["result"] = result
        if message:
            event_data["message"] = message
        if metadata:
            event_data.update(metadata)

        # V2架构：构建标准事件格式
        event = {
            "event": "step_end",
            "data": event_data
        }

        # 更新活跃步骤状态
        if step_id in self.active_steps:
            self.active_steps[step_id].update({
                "status": status,
                "end_time": timestamp,
                "result": result
            })

        # V2架构：发布到Redis Pub/Sub
        await self._publish(event)

        logger.info(f"步骤结束: {step_name} ({step_id}) - {status}")

    async def notify_final_result(self, final_data: Dict[str, Any]):
        """
        V2架构：推送最终结果事件

        Args:
            final_data: 最终结果数据
        """
        event = {
            "event": "complete",
            "data": final_data
        }
        await self._publish(event)

        # 发送流结束信号
        await self._publish({"event": "eos"})
        logger.info(f"最终结果已发布，会话结束: {self.session_id}")

    async def notify_error(self, error_message: str, step_name: str = "unknown"):
        """
        V2架构：推送错误事件

        Args:
            error_message: 错误消息
            step_name: 相关步骤名称
        """
        event = {
            "event": "error",
            "data": {
                "step_name": step_name,
                "message": error_message,
                "timestamp": datetime.now().isoformat()
            }
        }
        await self._publish(event)
        await self._publish({"event": "eos"})
        logger.error(f"错误事件已发布: {error_message}")

    async def notify_progress(
        self,
        step_id: str,
        progress: int,
        message: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        V2架构：推送进度更新事件

        Args:
            step_id: 步骤ID
            progress: 进度百分比 (0-100)
            message: 进度消息
            details: 详细信息
        """
        event_data = {
            "step_id": step_id,
            "progress": progress,
            "message": message,
            "timestamp": datetime.now().isoformat()
        }

        if details:
            event_data["details"] = details

        event = {
            "event": "step_progress",
            "data": event_data
        }

        await self._publish(event)

    def get_session_info(self) -> Dict[str, Any]:
        """
        V2架构：获取会话基本信息

        Returns:
            会话基本信息
        """
        return {
            "session_id": self.session_id,
            "channel": self.channel,
            "active_steps": self.active_steps,
            "redis_available": self.redis_client is not None
        }
